// Ant Colony Optimization Simulation Core
export const ANT_STATE_SEARCHING = 'searching';
export const ANT_STATE_RETURNING = 'returning';

export type AntState = typeof ANT_STATE_SEARCHING | typeof ANT_STATE_RETURNING;

export interface Vector {
    x: number;
    y: number;
}

export interface FoodSource extends Vector {
    amount: number;
    initialAmount: number;
}

export interface Ant {
    id: number;
    pos: Vector;
    velocity: Vector;
    angle: number;
    state: AntState;
    hasFood: boolean;
}

export interface SimulationConfig {
    antCount: number;
    speed: number;
    steerStrength: number;
    wanderStrength: number;
    homingStrength: number;
    pheromoneDepositRate: number;
    evaporationRate: number;
    sensorAngleDegrees: number;
    sensorDistance: number;
    foodSize: number;
    obstacleDetectionRadius: number;
    obstacleRepulsion: number;
}

export class AntColonyOptimization {
    public ants: Ant[] = [];
    public width: number;
    public height: number;
    public nestPosition: Vector;
    public foodSources: FoodSource[] = [];
    public obstacles: Vector[][] = [];
    public foodTrails: { [key: string]: number } = {};
    public homeTrails: { [key: string]: number } = {};
    private totalFoodCollected: number = 0;  // 总共收集的食物数量
    private initialTotalFood: number = 0;    // 初始食物总量
    
    private _config: SimulationConfig;
    private static readonly DEFAULT_FOOD_AMOUNT = 250;

    private startTime: number = Date.now();
    private completionTime: number | null = null;
    public nestPulseTime: number = 0;  // 用于巢穴反馈动画

    constructor(width: number, height: number, config?: Partial<SimulationConfig>) {
        this.width = width;
        this.height = height;
        this._config = {
            antCount: 200,
            speed: 1.8,
            steerStrength: 0.08,        // 降低转向强度，避免过度转向
            wanderStrength: 0.03,       // 降低随机游走强度
            homingStrength: 0.015,      // 适度提高归巢导向力
            pheromoneDepositRate: 1.2,  // 降低信息素沉积率
            evaporationRate: 0.95,      // 加快信息素衰减
            sensorAngleDegrees: 30,     // 减小感知角度
            sensorDistance: 15,         // 减小感知距离
            foodSize: 5,
            obstacleDetectionRadius: 15,
            obstacleRepulsion: 0.4,
            ...config
        };
        this.nestPosition = { x: width / 2, y: height / 2 };
        this.initializeAnts();
        this.startTime = Date.now();
    }

    get config(): SimulationConfig {
        return this._config;
    }

    set config(newConfig: SimulationConfig) {
        // 保存旧配置，以便处理蚂蚁数量变化
        const oldConfig = this._config;
        this._config = { ...newConfig };

        // 特殊处理蚂蚁数量变化
        if (newConfig.antCount !== oldConfig.antCount) {
            const diff = newConfig.antCount - oldConfig.antCount;
            if (diff > 0) {
                this.addAnts(diff);
            } else if (diff < 0) {
                this.removeAnts(-diff);
            }
        }
    }

    private initializeAnts() {
        this.ants = Array(this.config.antCount).fill(null).map((_, id) => ({
            id,
            pos: { x: this.nestPosition.x, y: this.nestPosition.y },
            velocity: this.randomVelocity(),
            angle: Math.random() * Math.PI * 2,
            state: ANT_STATE_SEARCHING,
            hasFood: false
        }));
    }

    private randomVelocity(): Vector {
        const angle = Math.random() * Math.PI * 2;
        return {
            x: Math.cos(angle) * this.config.speed,
            y: Math.sin(angle) * this.config.speed
        };
    }

    private normalize(v: Vector): Vector {
        const mag = Math.sqrt(v.x * v.x + v.y * v.y);
        return mag > 0 ? { x: v.x / mag, y: v.y / mag } : { x: 0, y: 0 };
    }

    private multiply(v: Vector, s: number): Vector {
        return { x: v.x * s, y: v.y * s };
    }

    private add(v1: Vector, v2: Vector): Vector {
        return { x: v1.x + v2.x, y: v1.y + v2.y };
    }

    private subtract(v1: Vector, v2: Vector): Vector {
        return { x: v1.x - v2.x, y: v1.y - v2.y };
    }

    private magnitude(v: Vector): number {
        return Math.sqrt(v.x * v.x + v.y * v.y);
    }

    public addFood(x: number, y: number) {
        this.foodSources.push({
            x,
            y,
            amount: AntColonyOptimization.DEFAULT_FOOD_AMOUNT,
            initialAmount: AntColonyOptimization.DEFAULT_FOOD_AMOUNT
        });
        this.initialTotalFood += AntColonyOptimization.DEFAULT_FOOD_AMOUNT;
    }

    private detectObstacle(ant: Ant): Vector | null {
        let totalRepulsion: Vector = { x: 0, y: 0 };

        for (const wall of this.obstacles) {
            for (let i = 0; i < wall.length - 1; i++) {
                const v = wall[i];
                const w = wall[i + 1];

                const l2 = Math.pow(v.x - w.x, 2) + Math.pow(v.y - w.y, 2);
                if (l2 === 0) continue;

                let t = ((ant.pos.x - v.x) * (w.x - v.x) + (ant.pos.y - v.y) * (w.y - v.y)) / l2;
                t = Math.max(0, Math.min(1, t));

                const projection = {
                    x: v.x + t * (w.x - v.x),
                    y: v.y + t * (w.y - v.y)
                };

                const dist = this.magnitude(this.subtract(ant.pos, projection));

                if (dist < this.config.obstacleDetectionRadius) {
                    const repulsionVector = this.normalize(this.subtract(ant.pos, projection));
                    const strength = (1 - dist / this.config.obstacleDetectionRadius) * this.config.obstacleRepulsion;
                    totalRepulsion = this.add(totalRepulsion, this.multiply(repulsionVector, strength));
                }
            }
        }

        return this.magnitude(totalRepulsion) > 0 ? totalRepulsion : null;
    }

    private handleBoundaries(ant: Ant) {
        if (ant.pos.x <= 1) {
            ant.pos.x = 1;
            ant.velocity.x = Math.abs(ant.velocity.x);
        } else if (ant.pos.x >= this.width - 1) {
            ant.pos.x = this.width - 1;
            ant.velocity.x = -Math.abs(ant.velocity.x);
        }
        
        if (ant.pos.y <= 1) {
            ant.pos.y = 1;
            ant.velocity.y = Math.abs(ant.velocity.y);
        } else if (ant.pos.y >= this.height - 1) {
            ant.pos.y = this.height - 1;
            ant.velocity.y = -Math.abs(ant.velocity.y);
        }
    }

    private sensePheromone(pos: Vector, type: 'food' | 'home'): number {
        if (pos.x < 0 || pos.x >= this.width || pos.y < 0 || pos.y >= this.height) return 0;
        const key = `${Math.floor(pos.x / 5)},${Math.floor(pos.y / 5)}`;
        return (type === 'food' ? this.foodTrails[key] : this.homeTrails[key]) || 0;
    }

    // 计算到目标的吸引力
    private calculateTargetAttraction(ant: Ant): Vector {
        const target = ant.hasFood ? this.nestPosition : this.findNearestFood(ant.pos);
        if (!target) return { x: 0, y: 0 };

        const toTarget = this.subtract(target, ant.pos);
        const distance = this.magnitude(toTarget);
        
        // 设定感知范围
        const maxAttractionRange = 100; // 最大感知范围
        const minAttractionRange = 20;  // 最小感知范围（强制吸引）
        
        if (distance > maxAttractionRange) return { x: 0, y: 0 };
        
        // 计算吸引力强度，距离越近越强
        let attractionStrength = 0;
        if (distance < minAttractionRange) {
            attractionStrength = 0.8; // 很近时强制吸引
        } else {
            attractionStrength = 0.2 * (1 - (distance - minAttractionRange) / (maxAttractionRange - minAttractionRange));
        }
        
        return this.multiply(this.normalize(toTarget), attractionStrength);
    }

    // 寻找最近的食物源
    private findNearestFood(pos: Vector): Vector | null {
        if (this.foodSources.length === 0) return null;
        
        let nearest = this.foodSources[0];
        let minDistance = this.magnitude(this.subtract(nearest, pos));
        
        for (const food of this.foodSources) {
            const distance = this.magnitude(this.subtract(food, pos));
            if (distance < minDistance) {
                minDistance = distance;
                nearest = food;
            }
        }
        
        return nearest;
    }

    private depositPheromone(ant: Ant) {
        const key = `${Math.floor(ant.pos.x / 5)},${Math.floor(ant.pos.y / 5)}`;
        
        // 根据蚂蚁与目标的距离调整信息素强度
        const target = ant.hasFood ? this.nestPosition : this.findNearestFood(ant.pos);
        let depositAmount = 1;

        if (target) {
            const distance = this.magnitude(this.subtract(ant.pos, target));
            const maxDistance = 200;
            
            // 距离目标越近，信息素越强
            depositAmount = Math.max(0.2, 1 - (distance / maxDistance));
            
            // 携带食物的蚂蚁留下更强的信息素
            if (ant.hasFood) depositAmount *= 1.5;
        }

        if (ant.hasFood) {
            this.homeTrails[key] = Math.min(10, (this.homeTrails[key] || 0) + depositAmount);
        } else {
            this.foodTrails[key] = Math.min(10, (this.foodTrails[key] || 0) + depositAmount);
        }
    }

    private steer(ant: Ant): Vector {
        const angle = Math.atan2(ant.velocity.y, ant.velocity.x);
        const sensorAngle = this.config.sensorAngleDegrees * Math.PI / 180;
        
        const makeSensor = (a: number) => ({
            x: ant.pos.x + Math.cos(a) * this.config.sensorDistance,
            y: ant.pos.y + Math.sin(a) * this.config.sensorDistance
        });

        const trailType = ant.hasFood ? 'home' : 'food';
        
        const forwardStrength = this.sensePheromone(makeSensor(angle), trailType);
        const leftStrength = this.sensePheromone(makeSensor(angle - sensorAngle), trailType);
        const rightStrength = this.sensePheromone(makeSensor(angle + sensorAngle), trailType);

        const maxStrength = Math.max(forwardStrength, leftStrength, rightStrength);
        if (maxStrength === 0) return { x: 0, y: 0 };

        if (forwardStrength >= maxStrength * 0.9) {
            return this.normalize(ant.velocity);
        }

        const targetAngle = leftStrength > rightStrength ? 
            angle - sensorAngle : 
            angle + sensorAngle;

        return this.normalize({
            x: Math.cos(targetAngle),
            y: Math.sin(targetAngle)
        });
    }

    private evaporateTrails() {
        [this.foodTrails, this.homeTrails].forEach(trails => {
            for (const key in trails) {
                const evaporationRate = this.config.evaporationRate ** (trails[key] > 10 ? 1.2 : 1);
                trails[key] *= evaporationRate;
                if (trails[key] < 0.1) {
                    delete trails[key];
                }
            }
        });
    }

    // 计算基于距离的速度修正因子
    private calculateSpeedFactor(ant: Ant): number {
        const target = ant.hasFood ? this.nestPosition : this.findNearestFood(ant.pos);
        if (!target) return 1.0;

        const distance = this.magnitude(this.subtract(target, ant.pos));
        
        // 设定速度调整范围
        const maxRange = 100;  // 开始减速的距离
        const minRange = 20;   // 最大减速距离
        
        if (distance > maxRange) return 1.0;  // 远离目标时保持正常速度
        if (distance < minRange) return 0.3;  // 非常接近目标时保持较低速度
        
        // 在减速范围内使用平方反比计算速度因子
        const normalizedDist = (distance - minRange) / (maxRange - minRange);
        const speedFactor = 0.3 + 0.7 * (normalizedDist * normalizedDist);  // 0.3~1.0的平方映射
        
        return speedFactor;
    }

    public update() {
        this.ants.forEach(ant => {
            const obstacleForce = this.detectObstacle(ant);
            
            if (obstacleForce) {
                ant.velocity = this.normalize(obstacleForce);
                ant.velocity = this.multiply(ant.velocity, this.config.speed);
            } else {
                const steerVector = this.multiply(this.steer(ant), this.config.steerStrength);
                
                const wanderStrength = ant.hasFood ? 
                    this.config.wanderStrength * 0.2 : 
                    this.config.wanderStrength * 0.5;
                
                const wanderAngle = Math.atan2(ant.velocity.y, ant.velocity.x) + (Math.random() * 2 - 1) * 0.5;
                const wanderVector = this.multiply({
                    x: Math.cos(wanderAngle),
                    y: Math.sin(wanderAngle)
                }, wanderStrength);

                // 计算目标吸引力
                const attractionVector = this.multiply(
                    this.calculateTargetAttraction(ant),
                    this.config.steerStrength * 3
                );

                const homingVector = ant.state === ANT_STATE_RETURNING ? 
                    this.multiply(
                        this.normalize(this.subtract(this.nestPosition, ant.pos)),
                        ant.hasFood ? this.config.homingStrength * 2 : this.config.homingStrength
                    ) : { x: 0, y: 0 };

                ant.velocity = this.normalize(this.add(
                    this.add(ant.velocity, steerVector),
                    this.add(
                        this.add(wanderVector, attractionVector),
                        homingVector
                    )
                ));

                // 应用基于距离的速度修正
                const speedFactor = this.calculateSpeedFactor(ant);
                ant.velocity = this.multiply(ant.velocity, this.config.speed * speedFactor);
            }

            ant.pos = this.add(ant.pos, ant.velocity);
            this.handleBoundaries(ant);
            ant.angle = Math.atan2(ant.velocity.y, ant.velocity.x);
            
            // Check for food interaction
            if (!ant.hasFood) {  // 搜索状态
                for (let i = this.foodSources.length - 1; i >= 0; i--) {
                    const food = this.foodSources[i];
                    const collisionRadius = this.config.foodSize + (food.amount / food.initialAmount * 5);
                    if (this.magnitude(this.subtract(ant.pos, food)) < collisionRadius) {
                        food.amount--;
                        ant.state = ANT_STATE_RETURNING;
                        ant.hasFood = true;
                        
                        if (food.amount <= 0) {
                            this.foodSources.splice(i, 1);
                        }
                        break;
                    }
                }
            } else if (this.magnitude(this.subtract(ant.pos, this.nestPosition)) < 15) {
                // 到达巢穴，增加收集计数
                this.totalFoodCollected++;
                ant.state = ANT_STATE_SEARCHING;
                ant.hasFood = false;
                // 触发巢穴反馈动画
                this.nestPulseTime = Date.now();
            }

            if (Math.random() < 2 / this.config.pheromoneDepositRate) {
                this.depositPheromone(ant);
            }
        });

        this.evaporateTrails();
        this.checkCompletion();
    }

    private checkCompletion() {
        const percentage = (this.totalFoodCollected / this.initialTotalFood) * 100;
        if (percentage >= 95 && this.completionTime === null) {
            this.completionTime = Date.now();
        }
    }

    public addAnts(count: number) {
        const currentCount = this.ants.length;
        for (let i = 0; i < count; i++) {
            this.ants.push({
                id: currentCount + i,
                pos: { x: this.nestPosition.x, y: this.nestPosition.y },
                velocity: this.randomVelocity(),
                angle: Math.random() * Math.PI * 2,
                state: ANT_STATE_SEARCHING,
                hasFood: false
            });
        }
    }

    public removeAnts(count: number) {
        this.ants.splice(-count, count);
    }

    public addObstacle(points: Vector[]) {
        this.obstacles.push(points);
    }

    public clearObstacles() {
        this.obstacles = [];
    }

    public reset() {
        this.initializeAnts();
        this.foodTrails = {};
        this.homeTrails = {};
        this.obstacles = [];
        this.foodSources = [];
        this.totalFoodCollected = 0;
        this.initialTotalFood = 0;
        this.startTime = Date.now();
        this.completionTime = null;
        this.nestPulseTime = 0;
    }

    public resize(width: number, height: number) {
        this.width = width;
        this.height = height;
        this.nestPosition = { x: width / 2, y: height / 2 };
    }

    public getStatistics() {
        const remainingFood = this.foodSources.reduce((sum, food) => sum + food.amount, 0);
        const percentage = this.initialTotalFood > 0 ? 
            (this.totalFoodCollected / this.initialTotalFood * 100) : 0;
        
        const currentTime = Date.now();
        const elapsedTime = (currentTime - this.startTime) / 1000;
        
        return {
            foodStats: {
                collected: this.totalFoodCollected,
                total: this.initialTotalFood,
                remaining: remainingFood,
                percentage: percentage.toFixed(1),
                elapsedTime: elapsedTime,
                isCompleted: this.completionTime !== null,
                completionTime: this.completionTime ? (this.completionTime - this.startTime) / 1000 : null
            },
            antStats: {
                searching: this.ants.filter(ant => !ant.hasFood).length,
                returning: this.ants.filter(ant => ant.hasFood).length,
                total: this.ants.length
            },
            nestPulseTime: this.nestPulseTime
        };
    }
}
