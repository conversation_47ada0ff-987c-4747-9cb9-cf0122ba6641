
import React from 'react';

const SchellingRulesExplanation: React.FC = () => {
  return (
    <div className="absolute bottom-4 left-4 right-4 bg-background/80 backdrop-blur-sm p-4 rounded-lg border text-foreground text-sm shadow-lg max-w-xl mx-auto pointer-events-none">
      <h3 className="font-bold text-lg mb-2 text-amber-400">谢林隔离模型解析</h3>
      <ul className="space-y-2">
        <li>
          <strong className="font-semibold">满意度 (Satisfaction):</strong>
          每个代理（小方块）会观察其邻居。如果<strong className="text-sky-400">同类</strong>邻居的比例达到或超过 <strong className="text-amber-400">满意度阈值</strong>，它就感到“满意”并保持不动。
        </li>
        <li>
          <strong className="font-semibold">搬迁 (Relocation):</strong>
          不满意的代理会随机搬到网格中的一个<strong className="text-gray-400">空位</strong>。
        </li>
        <li>
          <strong className="font-semibold">涌现 (Emergence):</strong>
          所有不满意的代理都完成搬迁后，一回合结束。重复此过程，你会发现即使个体只有温和的同类偏好，宏观上也会自发形成<strong className="text-amber-400">隔离的社区</strong>。
        </li>
      </ul>
      <p className="mt-3 text-xs text-muted-foreground">
        调整右侧参数，观察微观的个体决策如何演变为宏观的社会结构！
      </p>
    </div>
  );
};

export default SchellingRulesExplanation;
