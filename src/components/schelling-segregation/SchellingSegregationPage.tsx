
import React, { useState } from 'react';
import useSchellingModel from '@/hooks/useSchellingModel';
import SchellingCanvas from './SchellingCanvas';
import SchellingControlPanel from './SchellingControlPanel';
import SchellingRulesExplanation from './SchellingRulesExplanation';
import { SchellingParams } from '@/lib/schelling-segregation/core';

const initialParams: SchellingParams = {
  gridSize: 50,
  similarityThreshold: 0.35,
  ratioTypeA: 0.5,
  emptyCellsPct: 0.1,
  neighborhoodType: 'Moore',
  boundaryCondition: 'Periodic',
};

interface SchellingSegregationPageProps {
  isActive: boolean;
}

const SchellingSegregationPage: React.FC<SchellingSegregationPageProps> = ({ isActive }) => {
  const [params, setParams] = useState<SchellingParams>(initialParams);
  const { grid, stats, isRunning, togglePlay, step, reset } = useSchellingModel(params, isActive);

  const [openAccordion, setOpenAccordion] = useState<string | undefined>();

  const handleReset = () => {
    reset();
    setParams(initialParams);
  };

  // Convert SchellingStats to the expected format
  const controlPanelStats = {
    iteration: stats.round,
    unhappyAgents: Math.round((1 - stats.happyAgentPct) * 100), // Convert to count approximation
    totalAgents: Math.round(params.gridSize * params.gridSize * (1 - params.emptyCellsPct)),
    segregationIndex: stats.segregationIndex,
  };

  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-150px)] w-full bg-background">
      <div className="flex-grow h-full w-full md:w-auto relative border-2 border-amber-500/50 rounded-lg overflow-hidden">
        <SchellingCanvas
          grid={grid}
          agentAColor="#f59e0b" // amber-500
          agentBColor="#0ea5e9" // sky-500
          emptyCellColor="#374151" // gray-700
          latticeCount={params.gridSize}
        />
        {openAccordion === 'params' && <SchellingRulesExplanation />}
      </div>
      <SchellingControlPanel
        isRunning={isRunning}
        togglePlay={togglePlay}
        step={step}
        reset={handleReset}
        params={params}
        setParams={setParams}
        stats={controlPanelStats}
        openAccordion={openAccordion}
        setOpenAccordion={setOpenAccordion}
      />
    </div>
  );
};

export default SchellingSegregationPage;
