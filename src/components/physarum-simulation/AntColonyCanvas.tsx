import React, { useRef, useEffect, useCallback } from 'react';
import { AntColonyOptimization } from '@/lib/physarum/ant-colony';

interface AntColonyCanvasProps {
  simulation: AntColonyOptimization | null;
  onAddFood: (x: number, y: number) => void;
  onStartDrawing: (x: number, y: number) => void;
  onContinueDrawing: (x: number, y: number) => void;
  onStopDrawing: () => void;
  isDrawing: boolean;
}

const AntColonyCanvas: React.FC<AntColonyCanvasProps> = ({
  simulation,
  onAddFood,
  onStartDrawing,
  onContinueDrawing,
  onStopDrawing,
  isDrawing
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const foodCanvasRef = useRef<HTMLCanvasElement>(null);
  const homeCanvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();

  const getMousePos = useCallback((event: React.MouseEvent) => {
    if (!canvasRef.current) return { x: 0, y: 0 };
    const rect = canvasRef.current.getBoundingClientRect();
    return {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };
  }, []);

  const resizeCanvas = useCallback(() => {
    if (!canvasRef.current || !foodCanvasRef.current || !homeCanvasRef.current) return;
    const container = canvasRef.current.parentElement;
    if (!container) return;

    const width = container.clientWidth;
    const height = container.clientHeight;
    
    [canvasRef.current, foodCanvasRef.current, homeCanvasRef.current].forEach(canvas => {
      canvas.width = width;
      canvas.height = height;
    });

    if (simulation) {
      simulation.resize(width, height);
    }
  }, [simulation]);

  const render = useCallback(() => {
    if (!canvasRef.current || !foodCanvasRef.current || !homeCanvasRef.current || !simulation) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const foodCtx = foodCanvasRef.current.getContext('2d');
    const homeCtx = homeCanvasRef.current.getContext('2d');
    if (!ctx || !foodCtx || !homeCtx) return;

    // 清除所有画布
    [ctx, foodCtx, homeCtx].forEach(context => {
      context.clearRect(0, 0, canvas.width, canvas.height);
    });
    
    // 设置主画布背景
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 渲染食物信息素
    foodCtx.globalCompositeOperation = 'source-over';
    for (const key in simulation.foodTrails) {
      const [x, y] = key.split(',').map(n => parseInt(n) * 5);        const intensity = Math.min(simulation.foodTrails[key] / 10, 1) * 0.5; // 降低整体透明度
        if (intensity > 0.05) {
          foodCtx.fillStyle = `rgba(132, 204, 22, ${intensity})`; // 黄绿色 (#84cc16)
          foodCtx.fillRect(x, y, 5, 5);
        }
    }

    // 渲染返巢信息素
    homeCtx.globalCompositeOperation = 'source-over';
    for (const key in simulation.homeTrails) {
      const [x, y] = key.split(',').map(n => parseInt(n) * 5);
      const intensity = Math.min(simulation.homeTrails[key] / 10, 1);
      if (intensity > 0.1) {
        homeCtx.fillStyle = `rgba(59, 130, 246, ${intensity})`; // 蓝色
        homeCtx.fillRect(x, y, 5, 5);
      }
    }

    // 将信息素图层合并到主画布
    ctx.globalAlpha = 0.6;
    ctx.globalCompositeOperation = 'lighter';
    ctx.drawImage(foodCanvasRef.current, 0, 0);
    ctx.drawImage(homeCanvasRef.current, 0, 0);
    ctx.globalCompositeOperation = 'source-over';
    ctx.globalAlpha = 1.0;

    // 绘制巢穴（带脉动效果）
    const timeSinceLastPulse = Date.now() - simulation.nestPulseTime;
    const pulseRadius = timeSinceLastPulse < 300 ? 
      15 + Math.sin(timeSinceLastPulse / 300 * Math.PI) * 5 : 15;

    // 绘制巢穴主体
    ctx.fillStyle = '#3b82f6'; // 蓝色
    ctx.beginPath();
    ctx.arc(simulation.nestPosition.x, simulation.nestPosition.y, pulseRadius, 0, Math.PI * 2);
    ctx.fill();
    
    // 如果正在脉动，添加光晕效果
    if (timeSinceLastPulse < 300) {
      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 2;
      const glowRadius = pulseRadius + 5;
      const glowAlpha = 1 - (timeSinceLastPulse / 300);
      
      ctx.globalAlpha = glowAlpha;
      ctx.beginPath();
      ctx.arc(simulation.nestPosition.x, simulation.nestPosition.y, glowRadius, 0, Math.PI * 2);
      ctx.stroke();
      ctx.globalAlpha = 1.0;
    }

    // 绘制食物源，使用饼状图显示剩余量
    simulation.foodSources.forEach(food => {
      const foodRadius = 15; // 与巢穴大小相同
      const percentage = food.amount / food.initialAmount;
      const remainingAngle = Math.PI * 2 * percentage;
      
      // 绘制底层灰色圆表示总量
      ctx.fillStyle = '#374151'; // 深灰色
      ctx.beginPath();
      ctx.arc(food.x, food.y, foodRadius, 0, Math.PI * 2);
      ctx.fill();

      // 绘制黄色扇形表示剩余量
      if (percentage > 0) {
        ctx.fillStyle = '#eab308'; // 黄色
        ctx.beginPath();
        ctx.moveTo(food.x, food.y);
        ctx.arc(food.x, food.y, foodRadius, -Math.PI / 2, -Math.PI / 2 + remainingAngle);
        ctx.lineTo(food.x, food.y);
        ctx.fill();
      }

      // 添加发光边缘效果
      ctx.strokeStyle = '#fef08a'; // 浅黄色
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.arc(food.x, food.y, foodRadius, -Math.PI / 2, -Math.PI / 2 + remainingAngle);
      ctx.stroke();

      // 在中心添加小圆点
      ctx.fillStyle = '#fef08a'; // 浅黄色
      ctx.beginPath();
      ctx.arc(food.x, food.y, 3, 0, Math.PI * 2);
      ctx.fill();
    });

    // 绘制障碍物
    if (simulation.obstacles.length > 0) {
      ctx.strokeStyle = '#ef4444'; // 红色
      ctx.lineWidth = 3;
      ctx.lineCap = 'round';
      
      simulation.obstacles.forEach(wall => {
        if (wall.length > 1) {
          ctx.beginPath();
          ctx.moveTo(wall[0].x, wall[0].y);
          for (let i = 1; i < wall.length; i++) {
            ctx.lineTo(wall[i].x, wall[i].y);
          }
          ctx.stroke();
        }
      });
    }

    // 绘制蚂蚁
    simulation.ants.forEach(ant => {
      ctx.fillStyle = ant.hasFood ? '#fbbf24' : '#ffffff'; // 黄色或白色
      ctx.beginPath();
      ctx.arc(ant.pos.x, ant.pos.y, 2, 0, Math.PI * 2);
      ctx.fill();
    });
  }, [simulation]);

  const animate = useCallback(() => {
    if (simulation) {
      simulation.update();
    }
    render();
    animationFrameRef.current = requestAnimationFrame(animate);
  }, [simulation, render]);

  const handleMouseDown = (e: React.MouseEvent) => {
    const pos = getMousePos(e);
    if (e.shiftKey) {
      onStartDrawing(pos.x, pos.y);
    } else {
      onAddFood(pos.x, pos.y);
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDrawing && e.buttons === 1) {
      const pos = getMousePos(e);
      onContinueDrawing(pos.x, pos.y);
    }
  };

  const handleMouseUp = () => {
    if (isDrawing) {
      onStopDrawing();
    }
  };

  useEffect(() => {
    resizeCanvas();
    const handleResize = () => resizeCanvas();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [resizeCanvas]);

  useEffect(() => {
    if (simulation) {
      animationFrameRef.current = requestAnimationFrame(animate);
    }
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [animate, simulation]);

  return (
    <div className="relative w-full h-full">
      <canvas
        ref={foodCanvasRef}
        className="absolute inset-0 w-full h-full"
        style={{ display: 'none' }}
      />
      <canvas
        ref={homeCanvasRef}
        className="absolute inset-0 w-full h-full"
        style={{ display: 'none' }}
      />
      <canvas
        ref={canvasRef}
        className="w-full h-full bg-black cursor-crosshair"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      />
    </div>
  );
};

export default AntColonyCanvas;
