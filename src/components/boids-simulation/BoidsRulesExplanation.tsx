
import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const BoidsRulesExplanation: React.FC = () => (
  <div className="absolute bottom-4 left-4 max-w-xs md:max-w-md bg-background/80 p-4 rounded-lg text-xs md:text-sm shadow-lg backdrop-blur-sm">
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1">
        <AccordionTrigger className="text-base font-bold text-cyan-300">Boids 算法规则</AccordionTrigger>
        <AccordionContent className="space-y-2 text-foreground/90">
          <p>
            <strong>1. 分离 (Separation):</strong> 避免与邻近的同伴过于拥挤。
          </p>
          <p>
            <strong>2. 队列 (Alignment):</strong> 尝试与邻近同伴的平均飞行方向保持一致。
          </p>
          <p>
            <strong>3. 聚集 (Cohesion):</strong> 尝试向邻近同伴的平均位置（质心）移动。
          </p>
          <p>
            <strong>交互:</strong> 在画布上单击鼠标左键，可以在点击位置添加一个新的 Boid。
          </p>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  </div>
);

export default BoidsRulesExplanation;
