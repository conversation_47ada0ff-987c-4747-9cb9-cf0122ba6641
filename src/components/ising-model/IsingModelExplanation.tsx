
import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Info } from 'lucide-react';

const IsingModelExplanation = () => {
  return (
    <div className="absolute top-2 left-2 right-2 md:max-w-lg z-10">
      <Accordion type="single" collapsible className="w-full bg-background/60 backdrop-blur-sm border border-border rounded-lg px-4">
        <AccordionItem value="item-1" className="border-b-0">
          <AccordionTrigger>
            <div className="flex items-center gap-2 text-violet-400">
              <Info className="h-4 w-4" />
              <span>为什么在临界温度以下仍有自旋翻转？</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="text-sm text-muted-foreground">
            在临界温度 Tc (对于二维方格模型，约为 2.269) 以下，系统倾向于有序（大部分自旋同向），因为相邻自旋同向排列可以降低能量。然而，由于温度 T &gt; 0，系统存在热能，粒子会进行无规则的热运动。这种热扰动会以一定概率（p = exp(-ΔE/T)）导致即使能量会增加的自旋翻转发生。这保证了系统能够探索所有可能的状态，最终达到玻尔兹曼分布所描述的热力学平衡态，而不是仅仅陷入局部能量最低点。
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default IsingModelExplanation;
