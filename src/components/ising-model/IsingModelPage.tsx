
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createIsingGrid, metropolisStep, calculateTotalEnergy, calculateTotalMagnetization, calculateSusceptibility, initialParams, IsingParams } from '@/lib/ising-model/core';
import IsingModelCanvas from './IsingModelCanvas';
import IsingModelControlPanel from './IsingModelControlPanel';
import IsingModelPlots, { PlotDataPoint } from './IsingModelPlots';
import IsingModelExplanation from './IsingModelExplanation';

interface IsingModelPageProps {
  isActive: boolean;
}

const IsingModelPage: React.FC<IsingModelPageProps> = ({ isActive }) => {
  const [grid, setGrid] = useState(() => createIsingGrid(initialParams.gridSize, 'Random'));
  const [params, setParams] = useState<IsingParams>(initialParams);
  const [isRunning, setIsRunning] = useState(false);
  const [initialState, setInitialState] = useState<'Random' | 'Ordered'>('Random');
  const [currentEnergy, setCurrentEnergy] = useState(0);
  const [currentMagnetization, setCurrentMagnetization] = useState(0);
  const [stepsPerFrame, setStepsPerFrame] = useState(5.0);
  const [spinUpColor, setSpinUpColor] = useState('#ff6b6b');
  const [spinDownColor, setSpinDownColor] = useState('#4ecdc4');
  const [plotData, setPlotData] = useState<PlotDataPoint[]>([]);
  const [stepCount, setStepCount] = useState(0);
  
  const [thermalizationSteps, setThermalizationSteps] = useState(1000);  // 减少热化步数以更快响应
  const [samplingSteps, setSamplingSteps] = useState(5000);  // 减少采样步数以提高响应速度
  const [currentSusceptibility, setCurrentSusceptibility] = useState(0);

  // 修改：为每个温度维护独立的磁化强度历史记录
  const [temperatureMagnetizationMap, setTemperatureMagnetizationMap] = useState<Map<string, number[]>>(new Map());
  const [temperatureSusceptibilityMap, setTemperatureSusceptibilityMap] = useState<Map<string, number[]>>(new Map());

  const runningRef = useRef(isRunning);
  const animationFrameRef = useRef<number>();
  const lastUpdateTimeRef = useRef(0);
  const paramsRef = useRef(params);
  
  runningRef.current = isRunning;
  paramsRef.current = params;

  // 初始化能量和磁化强度
  useEffect(() => {
    const energy = calculateTotalEnergy(grid, params);
    const magnetization = calculateTotalMagnetization(grid);
    setCurrentEnergy(energy);
    setCurrentMagnetization(magnetization);
  }, [grid, params]);

  // 当页面变为非活跃状态时停止运行
  useEffect(() => {
    if (!isActive && animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      setIsRunning(false);
    }
  }, [isActive]);

  const runSimulation = useCallback((timestamp: number) => {
    if (!runningRef.current || !isActive) return;

    if (timestamp - lastUpdateTimeRef.current >= 30) {
      lastUpdateTimeRef.current = timestamp;
      
      setGrid(currentGrid => {
        const steps = Math.round(stepsPerFrame);
        let tempGrid = currentGrid;
        let tempEnergy = currentEnergy;
        let tempMagnetization = currentMagnetization;
        
        for (let i = 0; i < steps; i++) {
          const result = metropolisStep(tempGrid, paramsRef.current, tempEnergy, tempMagnetization);
          tempGrid = result.newGrid;
          tempEnergy = result.newEnergy;
          tempMagnetization = result.newMagnetization;
        }
        
        setCurrentEnergy(tempEnergy);
        setCurrentMagnetization(tempMagnetization);
        setStepCount(prev => {
          const newStepCount = prev + steps;
          
          // 只在热化完成后开始收集数据
          if (newStepCount > thermalizationSteps) {
            const currentTemp = paramsRef.current.temperature;
            const tempKey = currentTemp.toFixed(3);
            
            // 为当前温度收集磁化强度数据 - 注意：磁化率计算需要原始值，不能取绝对值！
            setTemperatureMagnetizationMap(prevMap => {
              const newMap = new Map(prevMap);
              const existingMagnetizations = newMap.get(tempKey) || [];
              const updatedMagnetizations = [...existingMagnetizations, tempMagnetization].slice(-samplingSteps);
              newMap.set(tempKey, updatedMagnetizations);
              
              // 每50步计算一次磁化率，提高响应速度
              if (updatedMagnetizations.length > 50 && newStepCount % 50 === 0) {
                const susceptibility = calculateSusceptibility(updatedMagnetizations, paramsRef.current.temperature, paramsRef.current.gridSize);
                setCurrentSusceptibility(susceptibility);
                
                // 使用滑动平均更新磁化率数据
                setTemperatureSusceptibilityMap(prevSusMap => {
                  const newSusMap = new Map(prevSusMap);
                  const existingValues = newSusMap.get(tempKey) || [];
                  const updatedValues = [...existingValues, susceptibility].slice(-20); // 保留最近20个值用于滑动平均
                  newSusMap.set(tempKey, updatedValues);
                  
                  // 计算滑动平均并更新绘图数据
                  const averageSusceptibility = updatedValues.reduce((sum, val) => sum + val, 0) / updatedValues.length;
                  
                  setPlotData(prevData => {
                    // 检查是否已存在该温度点的数据
                    const existingIndex = prevData.findIndex(point => Math.abs(point.T - currentTemp) < 0.001);
                    
                    if (existingIndex >= 0) {
                      // 更新现有温度点的磁化率值
                      const newData = [...prevData];
                      newData[existingIndex] = {
                        T: currentTemp,
                        chi: averageSusceptibility,
                        step: newStepCount
                      };
                      return newData;
                    } else {
                      // 添加新的温度点
                      return [...prevData, {
                        T: currentTemp,
                        chi: averageSusceptibility,
                        step: newStepCount
                      }].slice(-200); // 保留最近200个不同温度点
                    }
                  });
                  
                  return newSusMap;
                });
              }
              
              return newMap;
            });
          }
          
          return newStepCount;
        });
        
        return tempGrid;
      });
    }
    
    if (isActive) {
      animationFrameRef.current = requestAnimationFrame(runSimulation);
    }
  }, [isActive, stepsPerFrame, currentEnergy, currentMagnetization, thermalizationSteps, samplingSteps]);

  const togglePlay = useCallback(() => {
    if (!isActive) return;
    
    setIsRunning(!isRunning);
    if (!isRunning) {
      lastUpdateTimeRef.current = performance.now();
      animationFrameRef.current = requestAnimationFrame(runSimulation);
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }
  }, [isRunning, runSimulation, isActive]);

  const reset = useCallback(() => {
    setIsRunning(false);
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    const newGrid = createIsingGrid(params.gridSize, initialState);
    setGrid(newGrid);
    setCurrentEnergy(calculateTotalEnergy(newGrid, params));
    setCurrentMagnetization(calculateTotalMagnetization(newGrid));
    setStepCount(0);
    setCurrentSusceptibility(0);
    // 重置时清空所有温度相关的历史数据
    setTemperatureMagnetizationMap(new Map());
    setTemperatureSusceptibilityMap(new Map());
  }, [params.gridSize, initialState, params]);

  const handleFlipSpin = useCallback((x: number, y: number) => {
    if (isRunning || !isActive) return;
    
    setGrid(currentGrid => {
      const newGrid = currentGrid.map(row => [...row]);
      newGrid[y][x] *= -1;
      const newEnergy = calculateTotalEnergy(newGrid, params);
      const newMagnetization = calculateTotalMagnetization(newGrid);
      setCurrentEnergy(newEnergy);
      setCurrentMagnetization(newMagnetization);
      return newGrid;
    });
  }, [isRunning, params, isActive]);

  const clearPlots = useCallback(() => {
    setPlotData([]);
    setCurrentSusceptibility(0);
    // 清空所有温度相关的历史数据
    setTemperatureMagnetizationMap(new Map());
    setTemperatureSusceptibilityMap(new Map());
  }, []);

  // 当参数变化时重新计算能量和磁化强度
  useEffect(() => {
    setCurrentEnergy(calculateTotalEnergy(grid, params));
    setCurrentMagnetization(calculateTotalMagnetization(grid));
  }, [params, grid]);

  // 当网格大小变化时重置网格
  useEffect(() => {
    const newGrid = createIsingGrid(params.gridSize, initialState);
    setGrid(newGrid);
    setCurrentEnergy(calculateTotalEnergy(newGrid, params));
    setCurrentMagnetization(calculateTotalMagnetization(newGrid));
    setStepCount(0);
    setCurrentSusceptibility(0);
  }, [params.gridSize, initialState]);

  // 计算标准化磁化强度
  const normalizedMagnetization = currentMagnetization / (params.gridSize * params.gridSize);
  const isThermalized = stepCount > thermalizationSteps;

  return (
    <div className="flex flex-col lg:flex-row h-[calc(100vh-150px)] w-full bg-background">
      <div className="flex-grow h-full w-full lg:w-auto relative border-2 border-violet-500/50 rounded-lg overflow-hidden">
        <IsingModelExplanation />
        <IsingModelCanvas 
          grid={grid}
          onFlipSpin={handleFlipSpin}
          spinUpColor={spinUpColor}
          spinDownColor={spinDownColor}
        />
        <div className="absolute bottom-2 left-2 right-2 bg-background/80 backdrop-blur-sm rounded-lg p-2">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 text-xs">
            <div className="text-violet-400">
              <div className="font-semibold">步数</div>
              <div>{stepCount} {!isThermalized && '(热化中)'}</div>
            </div>
            <div className="text-violet-400">
              <div className="font-semibold">标准化磁化强度</div>
              <div>{normalizedMagnetization.toFixed(4)}</div>
            </div>
            <div className="text-violet-400">
              <div className="font-semibold">磁化率</div>
              <div>{currentSusceptibility.toFixed(6)}</div>
            </div>
            <div className="text-violet-400">
              <div className="font-semibold">温度</div>
              <div>{params.temperature.toFixed(3)} {params.temperature >= 2.2 && params.temperature <= 2.3 && '(临界区)'}</div>
            </div>
          </div>
        </div>
        {!isActive && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <p className="text-white text-lg">模拟已暂停</p>
          </div>
        )}
      </div>
      
      <div className="w-full lg:w-80 flex flex-col">
        <IsingModelControlPanel
          isRunning={isRunning}
          togglePlay={togglePlay}
          reset={reset}
          params={params}
          setParams={setParams}
          initialState={initialState}
          setInitialState={setInitialState}
          clearPlots={clearPlots}
          stepsPerFrame={stepsPerFrame}
          setStepsPerFrame={setStepsPerFrame}
          spinUpColor={spinUpColor}
          setSpinUpColor={setSpinUpColor}
          spinDownColor={spinDownColor}
          setSpinDownColor={setSpinDownColor}
          thermalizationSteps={thermalizationSteps}
          setThermalizationSteps={setThermalizationSteps}
          samplingSteps={samplingSteps}
          setSamplingSteps={setSamplingSteps}
        />
        <div className="flex-grow overflow-y-auto p-4">
          <IsingModelPlots data={plotData} />
        </div>
      </div>
    </div>
  );
};

export default IsingModelPage;
