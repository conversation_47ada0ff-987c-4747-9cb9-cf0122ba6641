
import React from 'react';

const RulesExplanation: React.FC = () => {
  return (
    <div className="absolute bottom-4 left-4 right-4 bg-background/80 backdrop-blur-sm p-4 rounded-lg border text-foreground text-sm shadow-lg max-w-xl mx-auto pointer-events-none">
      <h3 className="font-bold text-lg mb-2 text-primary">生命规则解析</h3>
      <ul className="space-y-2">
        <li>
          <strong className="font-semibold">存活 (Survival):</strong>
          一个<strong className="text-cyan-400">活细胞</strong>在邻居数量介于 <strong className="text-green-400">存活(最小)</ strong> 和 <strong className="text-green-400">存活(最大)</strong> 之间时，下一代将继续存活。
        </li>
        <li>
          <strong className="font-semibold">诞生 (Birth):</strong>
          一个<strong className="text-gray-400">死细胞</strong>在邻居数量介于 <strong className="text-green-400">诞生(最小)</ strong> 和 <strong className="text-green-400">诞生(最大)</strong> 之间时，下一代将“复活”成为活细胞。
        </li>
        <li>
          <strong className="font-semibold">死亡 (Death):</strong>
          不满足上述任一条件的细胞，在下一代将死亡（因为“孤立”或“拥挤”）。
        </li>
      </ul>
      <p className="mt-3 text-xs text-muted-foreground">
        尝试调整右侧面板的参数，观察不同规则如何涌现出千变万化的复杂世界！
      </p>
    </div>
  );
};

export default RulesExplanation;
