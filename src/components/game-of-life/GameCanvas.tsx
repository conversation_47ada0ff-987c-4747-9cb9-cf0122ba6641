import React, { useRef, useEffect, useState } from 'react';

interface GameCanvasProps {
  grid: number[][];
  width: number;
  height: number;
  setCellState: (row: number, col: number, state: number) => void;
  liveCellColor: string;
  deadCellColor: string;
  gridLineColor: string;
}

const GameCanvas: React.FC<GameCanvasProps> = ({
  grid,
  width,
  height,
  setCellState,
  liveCellColor,
  deadCellColor,
  gridLineColor
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [view, setView] = useState({ scale: 1, offsetX: 0, offsetY: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPosition, setLastPanPosition] = useState({ x: 0, y: 0 });
  const [isDrawing, setIsDrawing] = useState(false);
  const lastDrawnCell = useRef<{ row: number, col: number } | null>(null);

  const drawGrid = (ctx: CanvasRenderingContext2D, canvasWidth: number, canvasHeight: number) => {
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    ctx.save();
    ctx.fillStyle = deadCellColor;
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);
    
    ctx.translate(view.offsetX, view.offsetY);
    ctx.scale(view.scale, view.scale);

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        if (grid[y][x] === 1) {
          ctx.fillStyle = liveCellColor;
          ctx.fillRect(x, y, 1, 1);
        }
      }
    }

    if (view.scale > 4) {
      ctx.strokeStyle = gridLineColor;
      ctx.lineWidth = 1 / view.scale;
      ctx.beginPath();
      for (let x = 0; x <= width; x++) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
      }
      for (let y = 0; y <= height; y++) {
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
      }
      ctx.stroke();
    }
    
    ctx.restore();
  };

  // Effect to fit grid to canvas and handle resizing
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const fitToContainer = () => {
      const { width: canvasWidth, height: canvasHeight } = canvas.getBoundingClientRect();
      if (canvasWidth === 0 || canvasHeight === 0) return;

      canvas.width = canvasWidth;
      canvas.height = canvasHeight;
      
      const scaleX = canvasWidth / width;
      const scaleY = canvasHeight / height;
      const scale = Math.min(scaleX, scaleY);
      
      const offsetX = (canvasWidth - width * scale) / 2;
      const offsetY = (canvasHeight - height * scale) / 2;

      setView({ scale, offsetX, offsetY });
    };

    const resizeObserver = new ResizeObserver(fitToContainer);
    resizeObserver.observe(canvas);

    fitToContainer(); // Initial fit

    return () => resizeObserver.disconnect();
  }, [width, height]);

  // Effect to draw the grid when it or the view changes
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || canvas.width === 0 || canvas.height === 0) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    drawGrid(ctx, canvas.width, canvas.height);
  }, [grid, view, liveCellColor, deadCellColor, gridLineColor]);

  const getMousePos = (e: React.MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    const rect = canvas.getBoundingClientRect();
    return { x: e.clientX - rect.left, y: e.clientY - rect.top };
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 1 || e.ctrlKey) { // Middle mouse button or Ctrl+Click for panning
      setIsPanning(true);
      setLastPanPosition(getMousePos(e));
    } else if (e.button === 0) { // Left mouse button for drawing
        setIsDrawing(true);
        const pos = getMousePos(e);
        const col = Math.floor((pos.x - view.offsetX) / view.scale);
        const row = Math.floor((pos.y - view.offsetY) / view.scale);
        if (row >= 0 && row < height && col >= 0 && col < width) {
            setCellState(row, col, grid[row][col] === 1 ? 0 : 1);
            lastDrawnCell.current = { row, col };
        }
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    const pos = getMousePos(e);
    if (isPanning) {
      const dx = pos.x - lastPanPosition.x;
      const dy = pos.y - lastPanPosition.y;
      setView(v => ({ ...v, offsetX: v.offsetX + dx, offsetY: v.offsetY + dy }));
      setLastPanPosition(pos);
    } else if (isDrawing) {
        const col = Math.floor((pos.x - view.offsetX) / view.scale);
        const row = Math.floor((pos.y - view.offsetY) / view.scale);
        if (row >= 0 && row < height && col >= 0 && col < width) {
            if (!lastDrawnCell.current || lastDrawnCell.current.row !== row || lastDrawnCell.current.col !== col) {
                setCellState(row, col, grid[row][col] === 1 ? 0 : 1);
                lastDrawnCell.current = { row, col };
            }
        }
    }
  };

  const handleMouseUp = () => {
    setIsPanning(false);
    setIsDrawing(false);
    lastDrawnCell.current = null;
  };
  
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const pos = getMousePos(e);
    const scaleAmount = 1.1;
    const newScale = e.deltaY < 0 ? view.scale * scaleAmount : view.scale / scaleAmount;

    if (!canvasRef.current) return;
    const minScale = Math.min(canvasRef.current.width / width, canvasRef.current.height / height);
    const maxScale = 50;
    const clampedScale = Math.max(minScale, Math.min(newScale, maxScale));

    const worldX = (pos.x - view.offsetX) / view.scale;
    const worldY = (pos.y - view.offsetY) / view.scale;
    const newOffsetX = pos.x - worldX * clampedScale;
    const newOffsetY = pos.y - worldY * clampedScale;

    setView({ scale: clampedScale, offsetX: newOffsetX, offsetY: newOffsetY });
  };


  return (
    <canvas
      ref={canvasRef}
      className="w-full h-full cursor-pointer"
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onWheel={handleWheel}
      onContextMenu={(e) => e.preventDefault()}
    />
  );
};

export default GameCanvas;
